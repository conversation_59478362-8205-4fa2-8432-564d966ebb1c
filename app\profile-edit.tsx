import Button from "@/components/Button";
import InputField from "@/components/InputField";
import InputSelect from "@/components/InputSelect";
import MyDatePicker from "@/components/MyDatePicker";
import AuthHeader from "@/features/auth/components/AuthHeader";
import useAuth, { myUserProfileSchema } from "@/hooks/useAuth";
import { createHandleErrorDialog } from "@/lib/errors";
import globalStyles from "@/lib/globalStyles";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { ScrollView, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { z } from "zod";

const editProfileSchema = myUserProfileSchema.omit({
  id: true,
  email: true,
  emailVerified: true,
  name: true,
});

type EditUserParams = z.infer<typeof editProfileSchema>;

export default function EditProfileScreen() {
  const { user, userClient } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const insets = useSafeAreaInsets();

  const genderOptions = useMemo(
    () => [
      { name: "Masculino", value: "male" },
      { name: "Feminino", value: "female" },
      { name: "Prefiro não dizer", value: "prefer_not_say" },
    ],
    []
  );

  const [formFields, setFormFields] = useState<Partial<EditUserParams>>({
    firstName: user?.firstName,
    lastName: user?.lastName,
    phoneNumber: user?.phoneNumber,
    website: user?.website,
    gender: user?.gender,
    birthday: user?.birthday,
  });

  useEffect(() => {
    if (!user) return;

    setFormFields({
      firstName: user?.firstName,
      lastName: user?.lastName,
      phoneNumber: user?.phoneNumber,
      website: user?.website,
      gender: user?.gender,
      birthday: user?.birthday,
    });
  }, [user]);

  const handleChange = (fieldName: keyof typeof formFields, value: string) => {
    setFormFields({
      ...formFields,
      [fieldName]: value,
    });
  };

  const handleDateChange = (timestamp: number) => {
    setFormFields({
      ...formFields,
      birthday: new Date(timestamp),
    });
  };

  const handleSubmit = useCallback(async () => {
    try {
      if (!userClient || !user) return;
      setIsLoading(true);
      const prefixProtocol = (url?: string) =>
        !url
          ? url
          : ["http://", "https://"].some((prefix) => url.startsWith(prefix))
          ? url
          : `https://${url}`;

      formFields.website = prefixProtocol(formFields.website);

      const validatedData = editProfileSchema.parse(formFields);

      await userClient.update({
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        unsafeMetadata: {
          phoneNumber: validatedData.phoneNumber,
          website: validatedData.website,
          gender: validatedData.gender,
          birthday: validatedData.birthday?.toISOString(),
        },
      });

      router.back();
    } catch (err: any) {
      createHandleErrorDialog({
        title: "Erro ao atualizar perfil",
        message: "Ocorreu um erro ao atualizar o perfil, tente novamente.",
        error: err,
      });
    } finally {
      setIsLoading(false);
    }
  }, [userClient, user, formFields]);

  return (
    <>
      <LinearGradient
        colors={["#E9E8ED", "#ceccd7"]}
        locations={[0.5, 0.9]}
        style={{
          width: "100%",
          height: "100%",
          position: "absolute",
          flex: 1,
          flexDirection: "column",
          zIndex: -1,
        }}
      />
      <View style={{ flex: 1, paddingTop: insets.top, overflow: "hidden" }}>
        <ScrollView
          style={{ flex: 1 }} // Add flex: 1
          contentContainerStyle={{ paddingBottom: insets.bottom }} // Add bottom padding
        >
          <View
            style={{
              flex: 1,
              paddingTop: globalStyles.size.pageTop,
              paddingBottom: globalStyles.gap.xs,
              paddingHorizontal: globalStyles.gap.xs,
              gap: globalStyles.gap.md,
              justifyContent: "flex-start",
            }}
          >
            <AuthHeader description="Edite o seu perfil" />
            <View
              style={{
                flexDirection: "column",
                gap: globalStyles.gap.sm,
              }}
            >
              <InputField
                required
                theme="secondary"
                placeholder="Nome"
                defaultValue={formFields?.firstName}
                value={formFields.firstName}
                onChangeText={(txt) => handleChange("firstName", txt)}
              />
              <InputField
                required
                theme="secondary"
                placeholder="Sobrenome"
                defaultValue={formFields?.lastName}
                value={formFields.lastName}
                onChangeText={(txt) => handleChange("lastName", txt)}
              />
              <InputField
                theme="secondary"
                placeholder="Website"
                defaultValue={formFields?.website}
                value={formFields.website}
                onChangeText={(txt) => handleChange("website", txt)}
              />
              <InputField
                theme="secondary"
                placeholder="Número de telefone"
                defaultValue={formFields?.phoneNumber}
                value={formFields.phoneNumber}
                onChangeText={(txt) => handleChange("phoneNumber", txt)}
                keyboardType="phone-pad"
              />
              <InputSelect
                label="Gênero"
                items={genderOptions}
                selected={genderOptions.find(
                  (v) => v.value === formFields.gender
                )}
                onSelectItem={(item) => handleChange("gender", item.value)}
                theme="secondary"
                style={{
                  borderRadius: globalStyles.rounded.xs,
                  borderColor: "transparent",
                }}
                textStyle={{
                  color: globalStyles.rgba().primary1,
                }}
                defaultValue={formFields?.gender}
              />
              <MyDatePicker
                date={formFields.birthday}
                label="Data de nascimento"
                onChange={handleDateChange}
                theme="secondary"
                style={{
                  backgroundColor: globalStyles.colors.white,
                  borderRadius: globalStyles.rounded.xs,
                }}
                defaultValue={formFields?.birthday}
              />
            </View>
            <View style={{ gap: globalStyles.gap.xs, flexDirection: "row" }}>
              <Button
                isLoading={isLoading}
                text="Salvar alterações"
                onPress={handleSubmit}
                style={{ flex: 1 }}
              />
              <Button
                text="Cancelar"
                style={{
                  backgroundColor: globalStyles.rgba({ opacity: 0.6 }).light
                    .secondary,
                }}
                onPress={() => router.back()}
                disabled={isLoading}
              />
            </View>
          </View>
        </ScrollView>
      </View>
    </>
  );
}
